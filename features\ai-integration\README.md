# AI Integration Module

## Overview
The AI Integration module provides comprehensive artificial intelligence capabilities for the GPAce application, including Gemini API integration, image analysis, LaTeX conversion, and intelligent test feedback systems.

## Features
- **Gemini API Integration**: Advanced AI-powered research and content generation
- **Image Analysis**: Timetable and study space analysis using computer vision
- **LaTeX Conversion**: Mathematical notation rendering and conversion
- **Test Feedback**: Intelligent test analysis and feedback generation

## File Structure

```
features/ai-integration/
├── README.md                    # This file
├── gemini/                      # Gemini API integration
│   ├── gemini-api.js           # Main Gemini API service
│   └── googleGenerativeAI.js   # Google Generative AI wrapper
├── research/                    # Research and analysis tools
│   └── imageAnalyzer.js        # Image analysis capabilities
├── latex/                       # LaTeX processing
│   └── ai-latex-conversion.js  # LaTeX conversion utilities
└── ui/                         # User interface components
    ├── instant-test-feedback.html  # Test feedback interface
    ├── ai-search-response.css     # AI response styling
    └── test-feedback.css          # Test feedback styling
```

## APIs Used

### Google Gemini API
- **Purpose**: Content generation, research, and analysis
- **Models Supported**: 
  - gemini-2.0-flash (faster responses)
  - gemini-2.0-pro (more capable)
  - gemini-1.5-flash
  - gemini-1.5-pro
- **Configuration**: API key stored in localStorage
- **Rate Limiting**: Built-in quota management

### Firebase Integration
- **Authentication**: User session management
- **Firestore**: Data persistence and synchronization
- **Real-time Updates**: Cross-tab synchronization

## Key Components

### 1. Gemini API Service (`gemini/gemini-api.js`)
- Handles API communication with Google Gemini
- Provides research capabilities
- Manages model selection and configuration
- Includes error handling and retry logic

### 2. Image Analyzer (`research/imageAnalyzer.js`)
- Analyzes timetable images for schedule extraction
- Evaluates study spaces for optimal learning conditions
- Provides structured JSON output for analysis results
- Supports both browser and Node.js environments

### 3. LaTeX Conversion (`latex/ai-latex-conversion.js`)
- Configures MathJax for mathematical notation rendering
- Handles inline and display math expressions
- Provides safe typesetting functions

### 4. Test Feedback UI (`ui/instant-test-feedback.html`)
- Interactive interface for test analysis
- Supports both text input and image upload
- Real-time feedback generation
- Comprehensive results display with scoring

## Dependencies

### External Libraries
- **Bootstrap 5.3.2**: UI framework and components
- **Font Awesome 6.5.1**: Icons and visual elements
- **MathJax**: Mathematical notation rendering
- **jsPDF**: PDF generation for reports
- **html2canvas**: Screenshot and image capture
- **Marked**: Markdown parsing and rendering

### Internal Dependencies
- `../../../js/firebaseConfig.js`: Firebase configuration
- `../../../js/auth.js`: Authentication services
- `../../../js/firestore.js`: Database operations
- `../../../js/api-settings.js`: API configuration management
- `../../../js/test-feedback.js`: Test analysis logic

## Configuration

### API Settings
The module requires configuration of API keys through the settings modal:

1. **Gemini API Key**: Required for AI functionality
2. **Wolfram Alpha API Key**: Optional for mathematical analysis
3. **Tavily API Key**: Optional for enhanced search capabilities

### Model Selection
Users can choose from different Gemini models based on their needs:
- **Flash models**: Faster response times, suitable for quick queries
- **Pro models**: More sophisticated analysis, better for complex tasks

### Response Creativity
Adjustable temperature setting (0.0 - 1.0) controls response creativity:
- **0.0-0.3**: Precise, factual responses
- **0.4-0.6**: Balanced creativity and accuracy
- **0.7-1.0**: More creative and varied responses

## Usage Examples

### Basic Research Query
```javascript
const results = await window.geminiApi.research({
    query: "machine learning fundamentals",
    maxResults: 5,
    model: "gemini-2.0-flash"
});
```

### Image Analysis
```javascript
const analyzer = new ImageAnalyzer();
const analysis = await analyzer.analyzeTimetable('path/to/timetable.jpg');
```

### Test Feedback
1. Navigate to the test feedback interface
2. Upload test content (text or images)
3. Configure analysis options (subject, test type, detail level)
4. Review generated feedback and suggestions

## Known Issues

### Current Limitations
- Image analysis requires stable internet connection
- Large image files may cause processing delays
- API rate limits may affect heavy usage
- Some mathematical notation may require manual formatting

### Browser Compatibility
- Modern browsers with ES6+ support required
- WebRTC capabilities needed for image processing
- Local storage required for settings persistence

## Dependencies on Other Modules

### Required Global Services
- **Firebase**: Authentication and data storage
- **Main CSS**: Base styling and theme variables
- **Side Drawer**: Navigation integration
- **Cross-tab Sync**: Real-time data synchronization

### Optional Integrations
- **Study Spaces**: Enhanced location analysis
- **Calendar**: Schedule integration
- **Flashcards**: AI-generated study materials

## Testing

### Manual Testing
1. Verify API key configuration saves correctly
2. Test image upload and analysis functionality
3. Validate test feedback generation
4. Check responsive design on different screen sizes

### Integration Testing
- Ensure Firebase authentication works
- Verify cross-module navigation
- Test data persistence across sessions

## Future Enhancements

### Planned Features
- **Voice Input**: Speech-to-text for test content
- **Batch Processing**: Multiple file analysis
- **Advanced Analytics**: Learning pattern recognition
- **Collaborative Features**: Shared study sessions

### Performance Optimizations
- **Caching**: API response caching for repeated queries
- **Lazy Loading**: On-demand component loading
- **Image Compression**: Automatic image optimization
- **Background Processing**: Non-blocking analysis

## Support

For issues related to this module:
1. Check API key configuration
2. Verify internet connectivity
3. Review browser console for errors
4. Ensure all dependencies are loaded

## Version History

- **v1.0**: Initial modular implementation
- **v1.1**: Enhanced error handling and user feedback
- **v1.2**: Added support for multiple Gemini models
- **v1.3**: Improved image analysis capabilities

---

**Note**: This module is part of the GPAce application's modular architecture. Ensure all dependencies are properly configured before use.
